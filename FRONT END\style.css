/* Cyber Hacker Theme CSS */
body {
    background: #101820;
    color: #39ff14;
    font-family: 'Fira Mono', '<PERSON>sol<PERSON>', 'Courier New', monospace;
    margin: 0;
    padding: 0;
}

h2 {
    text-align: center;
    color: #39ff14;
    text-shadow: 0 0 10px #39ff14, 0 0 20px #39ff14;
    letter-spacing: 2px;
}

form {
    background: rgba(16, 24, 32, 0.95);
    max-width: 500px;
    margin: 40px auto;
    padding: 30px 40px;
    border-radius: 10px;
    box-shadow: 0 0 30px #39ff14, 0 0 10px #222 inset;
    border: 2px solid #39ff14;
}

input[type="text"],
input[type="date"] {
    width: 60%;
    padding: 7px;
    margin-bottom: 25px; /* Increased spacing */
    border: 1.5px solid #39ff14;
    border-radius: 4px;
    font-size: 1em;
    background: #181f2a;
    color: #39ff14;
    box-shadow: 0 0 5px #39ff14 inset;
    outline: none;
    transition: border 0.2s, box-shadow 0.2s;
}

input[type="text"]:focus,
input[type="date"]:focus {
    border: 1.5px solid #fff;
    box-shadow: 0 0 10px #39ff14, 0 0 5px #fff inset;
}

button[type="submit"] {
    background: #101820;
    color: #39ff14;
    border: 2px solid #39ff14;
    padding: 10px 25px;
    border-radius: 5px;
    font-size: 1em;
    cursor: pointer;
    box-shadow: 0 0 10px #39ff14, 0 0 5px #222 inset;
    text-shadow: 0 0 5px #39ff14;
    transition: background 0.2s, color 0.2s, box-shadow 0.2s;
}

button[type="submit"]:hover {
    background: #39ff14;
    color: #101820;
    box-shadow: 0 0 20px #39ff14, 0 0 10px #fff inset;
}

fieldset {
    border: 1.5px solid #39ff14;
    border-radius: 8px;
    margin-bottom: 40px;   /* Increased space between fieldsets */
    padding: 32px 24px 24px 24px; /* More padding inside fieldset */
    box-shadow: 0 0 8px #39ff14 inset;
    background: rgba(16, 24, 32, 0.98); /* Optional: subtle background for clarity */
    position: relative;
}

legend {
    font-weight: bold;
    color: #39ff14;
    text-shadow: 0 0 5px #39ff14;
    letter-spacing: 1px;
    padding: 0 20px;      /* More horizontal padding */
    margin-bottom: 18px;  /* More space below legend */
    background: #101820;  /* Optional: background to make legend pop */
    border-radius: 6px;
    position: relative;
    top: 8px;
}

label {
    display: inline-block;
    width: 120px;
    margin-bottom: 10px;
    color: #39ff14;
    text-shadow: 0 0 3px #39ff14;
}